@import "tailwindcss";

:root {
  --background: #FFFFFF;
  --foreground: #032221;

  /* Simplified Sustainable Fashion Color Palette */

  /* Base Colors */
  --color-white: #FFFFFF;              /* White - Base/background color */
  --color-dark-green: #032221;         /* Dark Green - Primary accent color */
  --color-pine-green: #01796F;         /* Pine Green - Secondary accent color */

  /* Semantic Color Mappings for shadcn/ui */
  --primary: var(--color-dark-green);
  --primary-foreground: var(--color-white);
  --secondary: var(--color-pine-green);
  --secondary-foreground: var(--color-white);
  --accent: var(--color-pine-green);
  --accent-foreground: var(--color-white);
  --muted: #F8F9FA;
  --muted-foreground: #6B7280;
  --destructive: #DC2626;
  --destructive-foreground: var(--color-white);
  --border: #E5E7EB;
  --input: #E5E7EB;
  --ring: var(--color-dark-green);
  --card: var(--color-white);
  --card-foreground: var(--color-dark-green);
  --popover: var(--color-white);
  --popover-foreground: var(--color-dark-green);

  /* Gradient variations */
  --gradient-primary: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 100%);
  --gradient-hero: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 100%);
  --gradient-dark: linear-gradient(135deg, var(--color-dark-green) 0%, rgba(1, 121, 111, 0.8) 100%);
  --gradient-overlay: linear-gradient(135deg, rgba(3, 34, 33, 0.9) 0%, rgba(1, 121, 111, 0.9) 100%);
}



@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-dark-green);
    --foreground: var(--color-white);
    --card: var(--color-dark-green);
    --card-foreground: var(--color-white);
    --popover: var(--color-dark-green);
    --popover-foreground: var(--color-white);
    --muted: rgba(1, 121, 111, 0.1);
    --muted-foreground: #9CA3AF;
    --border: rgba(1, 121, 111, 0.2);
    --input: rgba(1, 121, 111, 0.2);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Hierarchy */
.font-heading {
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 600; /* Semi Bold equivalent */
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.font-subheading {
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 500; /* Medium */
  line-height: 1.4;
  letter-spacing: -0.015em;
}

.font-body {
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 400; /* Regular */
  line-height: 1.6;
}

/* Enhanced animations and transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
}

/* Modern CSS Effects and Utilities */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors-smooth {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-transform-smooth {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale-sm:hover {
  transform: scale(1.02);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-pine-green) 0%, var(--color-dark-green) 100%);
}

.gradient-hero {
  background: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 50%, var(--color-dark-green) 100%);
}

.gradient-overlay {
  background: linear-gradient(135deg, rgba(3, 34, 33, 0.9) 0%, rgba(1, 121, 111, 0.8) 100%);
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-pine-green) 0%, var(--color-dark-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(3, 34, 33, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(3, 34, 33, 0.2);
}

/* Enhanced shadows */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-strong {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);
}

.shadow-colored {
  box-shadow: 0 4px 25px -5px rgba(1, 121, 111, 0.25), 0 10px 10px -5px rgba(1, 121, 111, 0.1);
}

/* Focus states */
.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-white), 0 0 0 4px var(--color-pine-green);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-white);
}

::-webkit-scrollbar-thumb {
  background: var(--color-pine-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-green);
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth transitions for interactive elements */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text effect */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Additional utility classes for the new color scheme */
.bg-brand-gradient {
  background: var(--gradient-primary);
}

.bg-brand-hero {
  background: var(--gradient-hero);
}

.text-brand-primary {
  color: var(--color-dark-green);
}

.text-brand-secondary {
  color: var(--color-pine-green);
}

.border-brand-primary {
  border-color: var(--color-dark-green);
}

.border-brand-secondary {
  border-color: var(--color-pine-green);
}

/* Typography classes */
.font-heading {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 600; /* Semi Bold */
}

.font-subheading {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 500; /* Medium */
}

.font-body {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 400; /* Regular */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--color-pine-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-green);
}
