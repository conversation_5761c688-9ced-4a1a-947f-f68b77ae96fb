'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { ArrowR<PERSON>, Play, Sparkles, Recycle, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Hero() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced background with gradient overlay */}
      <div className="absolute inset-0 gradient-hero">
        {/* Enhanced animated background elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-brand-pine-green/20 rounded-full animate-float shadow-soft"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-brand-white/10 rounded-full animate-float shadow-soft" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-brand-pine-green/30 rounded-full animate-float shadow-soft" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-60 left-1/3 w-8 h-8 bg-brand-white/5 rounded-full animate-float" style={{ animationDelay: '3s' }}></div>
        <div className="absolute bottom-60 right-1/3 w-14 h-14 bg-brand-pine-green/15 rounded-full animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-brand-white/5 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>

        {/* Gradient mesh overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-pine-green/10 to-transparent animate-shimmer"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Enhanced Badge */}
          <div className={`inline-flex items-center space-x-2 glass rounded-full px-6 py-3 mb-8 border border-brand-white/30 shadow-soft transition-all duration-1000 delay-100 hover-scale ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Sparkles className="h-5 w-5 text-brand-pine-green animate-pulse-slow" />
            <span className="text-brand-white text-sm font-subheading tracking-wide">Kenya&apos;s First Sustainable Fashion Platform</span>
          </div>

          {/* Enhanced Main Headline */}
          <h1 className={`text-4xl sm:text-5xl lg:text-7xl font-heading text-brand-white mb-6 leading-tight transition-all duration-1000 delay-200 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            The Future of Fashion is{' '}
            <span className="relative">
              <span className="gradient-text bg-gradient-to-r from-brand-pine-green via-green-400 to-brand-pine-green bg-clip-text text-transparent">
                Circular
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-brand-pine-green to-green-400 rounded-full opacity-60"></div>
            </span>
          </h1>

          {/* Subtitle */}
          <p className={`text-xl sm:text-2xl text-brand-white/90 mb-8 max-w-3xl mx-auto leading-relaxed font-body transition-all duration-1000 delay-400 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Join Kenya&apos;s sustainable clothing exchange platform. Swap, donate, and discover pre-loved fashion
            while earning Pedi tokens and making a positive impact on our environment.
          </p>

          {/* CTA Buttons */}
          <div className={`flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12 transition-all duration-1000 delay-600 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Link href="/auth">
              <Button
                size="lg"
                className="bg-brand-pine-green hover:bg-brand-dark-green text-brand-white px-12 py-6 text-lg font-subheading hover-lift group rounded-xl shadow-colored hover:shadow-strong transition-all-smooth"
              >
                Start Your Journey
                <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform-smooth" />
              </Button>
            </Link>

            <Button
              variant="ghost"
              size="lg"
              className="text-brand-white hover:bg-brand-white/10 px-10 py-5 text-lg font-subheading group rounded-xl border border-brand-white/20"
            >
              <Play className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className={`grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto transition-all duration-1000 delay-800 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <div className="text-center">
              <div className="text-3xl font-heading text-brand-white mb-2">10K+</div>
              <div className="text-brand-white/70 font-body">Clothes Exchanged</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-heading text-brand-white mb-2">5K+</div>
              <div className="text-brand-white/70 font-body">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-heading text-brand-white mb-2">2.5T</div>
              <div className="text-brand-white/70 font-body">CO₂ Saved (kg)</div>
            </div>
          </div>
        </div>

        {/* Floating Action Icons */}
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
          <div className="flex space-x-4">
            <div className="bg-brand-white/15 backdrop-blur-sm rounded-full p-4 animate-float border border-brand-white/20">
              <Recycle className="h-6 w-6 text-brand-pine-green" />
            </div>
            <div className="bg-brand-white/15 backdrop-blur-sm rounded-full p-4 animate-float border border-brand-white/20" style={{ animationDelay: '1s' }}>
              <Heart className="h-6 w-6 text-brand-pine-green" />
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-brand-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-brand-white/50 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
