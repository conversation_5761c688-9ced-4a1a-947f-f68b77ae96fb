'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Heart, 
  Eye, 
  MapPin, 
  Coins, 
  Recycle, 
  Gift,
  Star,
  User
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  originalPrice?: number;
  images: string[];
  views: number;
  likes: string[];
  likeCount: number;
  tags: string[];
  location: {
    county: string;
    town: string;
  };
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    rating: number;
    sustainabilityScore: number;
  };
  createdAt: string;
  isAvailable: boolean;
}

interface ClothingGridProps {
  items: ClothingItem[];
  loading?: boolean;
  onLike?: (itemId: string) => void;
  currentUserId?: string;
}

const getExchangeTypeIcon = (type: string) => {
  switch (type) {
    case 'swap':
      return <Recycle className="h-4 w-4" />;
    case 'token':
      return <Coins className="h-4 w-4" />;
    case 'donation':
      return <Gift className="h-4 w-4" />;
    default:
      return <Recycle className="h-4 w-4" />;
  }
};

const getExchangeTypeColor = (type: string) => {
  switch (type) {
    case 'swap':
      return 'bg-green-100 text-green-800';
    case 'token':
      return 'bg-blue-100 text-blue-800';
    case 'donation':
      return 'bg-purple-100 text-purple-800';
    case 'all':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getConditionColor = (condition: string) => {
  switch (condition) {
    case 'New':
      return 'bg-brand-pine-green/10 text-brand-pine-green border border-brand-pine-green/20';
    case 'Like New':
      return 'bg-brand-dark-green/10 text-brand-dark-green border border-brand-dark-green/20';
    case 'Good':
      return 'bg-yellow-50 text-yellow-700 border border-yellow-200';
    case 'Fair':
      return 'bg-orange-50 text-orange-700 border border-orange-200';
    case 'Poor':
      return 'bg-red-50 text-red-700 border border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border border-gray-200';
  }
};

export default function ClothingGrid({ items, loading = false, onLike, currentUserId }: ClothingGridProps) {
  const [likedItems, setLikedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (currentUserId) {
      const liked = new Set(
        items
          .filter(item => item.likes.includes(currentUserId))
          .map(item => item._id)
      );
      setLikedItems(liked);
    }
  }, [items, currentUserId]);

  const handleLike = async (itemId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!currentUserId || !onLike) return;

    try {
      await onLike(itemId);
      setLikedItems(prev => {
        const newSet = new Set(prev);
        if (newSet.has(itemId)) {
          newSet.delete(itemId);
        } else {
          newSet.add(itemId);
        }
        return newSet;
      });
    } catch (error) {
      console.error('Error liking item:', error);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="overflow-hidden animate-pulse">
            <div className="aspect-square bg-gray-200" />
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded" />
                <div className="h-3 bg-gray-200 rounded w-2/3" />
                <div className="h-3 bg-gray-200 rounded w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Recycle className="h-16 w-16 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
        <p className="text-gray-500">Try adjusting your search filters or browse different categories.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {items.map((item) => (
        <Link key={item._id} href={`/item/${item._id}`}>
          <Card className="overflow-hidden hover:shadow-strong transition-all-smooth cursor-pointer group hover-lift bg-brand-white border border-gray-200/50 hover:border-brand-pine-green/30">
            {/* Image */}
            <div className="relative aspect-square overflow-hidden">
              <img
                src={item.images[0]}
                alt={item.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform-smooth"
              />
              
              {/* Like Button */}
              {currentUserId && onLike && (
                <button
                  onClick={(e) => handleLike(item._id, e)}
                  className={`absolute top-3 right-3 p-2 rounded-full transition-all-smooth shadow-soft hover-scale ${
                    likedItems.has(item._id)
                      ? 'bg-red-500 text-white shadow-colored'
                      : 'bg-white/90 text-gray-600 hover:bg-white hover:shadow-medium'
                  }`}
                >
                  <Heart className={`h-4 w-4 ${likedItems.has(item._id) ? 'fill-current' : ''}`} />
                </button>
              )}

              {/* Exchange Type Badge */}
              <div className="absolute top-3 left-3">
                <Badge className={`${getExchangeTypeColor(item.exchangeType)} flex items-center gap-1`}>
                  {getExchangeTypeIcon(item.exchangeType)}
                  {item.exchangeType === 'all' ? 'All' : item.exchangeType}
                </Badge>
              </div>

              {/* Token Price */}
              {(item.exchangeType === 'token' || item.exchangeType === 'all') && item.tokenPrice && (
                <div className="absolute bottom-3 left-3">
                  <Badge className="bg-blue-600 text-white flex items-center gap-1">
                    <Coins className="h-3 w-3" />
                    {item.tokenPrice}
                  </Badge>
                </div>
              )}
            </div>

            <CardContent className="p-4">
              {/* Title and Description */}
              <div className="mb-3">
                <h3 className="font-semibold text-gray-900 line-clamp-1 mb-1">
                  {item.title}
                </h3>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {item.description}
                </p>
              </div>

              {/* Details */}
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className="text-xs">
                  {item.size}
                </Badge>
                <Badge className={`text-xs ${getConditionColor(item.condition)}`}>
                  {item.condition}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {item.color}
                </Badge>
              </div>

              {/* Owner Info */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                    {item.owner.profilePicture ? (
                      <img
                        src={item.owner.profilePicture}
                        alt={`${item.owner.firstName} ${item.owner.lastName}`}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User className="h-3 w-3 text-gray-500" />
                    )}
                  </div>
                  <span className="text-xs text-gray-600">
                    {item.owner.firstName} {item.owner.lastName}
                  </span>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    <span className="text-xs text-gray-600">{item.owner.rating.toFixed(1)}</span>
                  </div>
                </div>
              </div>

              {/* Location and Stats */}
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  <span>{item.location.county}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    <span>{item.views}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    <span>{item.likeCount}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
}
